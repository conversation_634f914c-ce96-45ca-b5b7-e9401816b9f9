interface EventDetailRow {
  Field: string;
  'Expected Value': string;
}

const eventDetailSelectors: {
  [key: string]: {
    selector: string;
    assertionType: 'have.text' | 'contain.text';
  };
} = {
  'Event Name': { selector: 'home-detail-name', assertionType: 'have.text' },
  'Date': { selector: 'home-detail-created-date', assertionType: 'have.text' },
  'Event Creator': { selector: 'home-detail-creator-name', assertionType: 'have.text' },
  'File Count': { selector: 'home-detail-file-count', assertionType: 'have.text' },
  'Match Group Count': { selector: 'home-detail-matchgroup-count', assertionType: 'have.text' },
  'View Event Button': { selector: 'home-view-event-button', assertionType: 'contain.text' },
  'Delete Event Button': { selector: 'home-delete-event-button', assertionType: 'contain.text' },
}

export const mainPage = {
  verifyColumnHeaders: () => {
    cy.getDataIdCy({ idAlias: 'column-0' }).should('be.visible')
      .and('contain.text', 'Event Name');
    cy.getDataIdCy({ idAlias: 'column-1' }).should('be.visible')
      .and('contain.text', 'Event Time');
  },

  verifyAppTitle: () => {
    cy.getDataIdCy({ idAlias: 'appbarTitle' }).should('be.visible')
      .should('have.text', 'Track');
  },

  clickTab: (tabName: string) =>
    cy.get(`[data-testid="home-${tabName.toLowerCase()}-tab"]`).click(),

  verifyTabIsActive: (activeTabName: string) => {
    const activeTab = activeTabName.toLowerCase();
    const inactiveTab = activeTab === 'events' ? 'files' : 'events';

    cy.get(`[data-testid="home-${activeTab}-tab"]`)
      .should('have.attr', 'aria-selected', 'true');
    cy.get(`[data-testid="home-${inactiveTab}-tab"]`)
      .should('have.attr', 'aria-selected', 'false');
  },

  clickBreadcrumb: (breadcrumbText: string) => {
    cy.url().as('initialUrl');
    cy.contains('nav[aria-label="breadcrumb"] a', breadcrumbText).click({ force: true });
  },

  verifyPageNotNavigated: () => {
    cy.get('@initialUrl').then((initialUrl) => {
      cy.url().should('eq', initialUrl);
    });
    cy.contains('nav[aria-label="breadcrumb"] a', 'All Events').should('be.visible');
  },

  selectEvent: (eventName: string) => {
    cy.log(`--- Selecting event: ${eventName} ---`);
    cy.contains('[data-testid^="event-row-"]', eventName)
      .should('be.visible')
      .click();
  },

  verifyEventDetails: (rows: EventDetailRow[]) => {
    cy.log('Starting event details verification...');

    rows.forEach((row: EventDetailRow) => {
      const field = row.Field;
      const expectedValue = row['Expected Value'];
      const detailConfig = eventDetailSelectors[field];

      if (!detailConfig) {
        throw new Error(`The field "${field}" is not a valid option to check in event details.`);
      }

      const { selector, assertionType } = detailConfig;

      cy.log(`Verifying "${field}" with expected value: "${expectedValue}" using selector: "${selector}"`);

      cy.getDataIdCy({ idAlias: selector })
        .should('be.visible')
        .and(assertionType, expectedValue);
    });
    cy.log('All event details verified successfully.');
  }

};
