import { v4 as uuidV4 } from 'uuid';
import { createAppSlice } from '../../createAppSlice';
import { Tracklet } from '@shared-types/tracker';
import { PayloadAction } from '@reduxjs/toolkit';
import {
  CreateMatchGroup,
  EventState,
  FileState,
  MatchGroupState,
  MatchGroupsState,
  UpdateMatchGroup,
  SelectedTrackletsResults,
  FileFilterState,
  BoundingBoxesState,
} from './types';
import { RootState } from '@store/store';
import HttpClient from '@store/dependencies/httpClient';
import getApiAuthToken from '@utility/getApiAuthToken';
import {
  AlertLevel,
  createSnackNotification,
} from '@components/common/Snackbar/Snackbar';
import {
  CreateMatchGroupResponse,
  DeleteTrackletsResponse,
  GetBoundingBoxesResponse,
  GetEventResponse,
  GetFileResponse,
  GetMatchGroupResponse,
  GetMatchGroupSelectedTrackletsResponse,
  GetMatchGroupsResponse,
  GetThumbnailsResponse,
  DeleteMatchGroupResponse,
} from '@shared-types/responses';
import qs from 'qs';
import { GetMatchGroupsParams } from '../event/types';
import { SearchMatchGroupsQueryParams } from '@shared-types/requests';
import { getEventById, updateMatchGroupAttributeSearch } from '../event/slice';
import ls from 'localstorage-slim';

export interface matchGroupSliceState {
  matchSelectedTracklets: SelectedTrackletsResults;
  matchGroups: MatchGroupsState;
  createMatchGroup: CreateMatchGroup;
  updateMatchGroup: UpdateMatchGroup;
  event: EventState;
  matchGroup: MatchGroupState;
  userSelectedTracklets?: Tracklet[];
  userSelectedTrackletFile: FileState;
  fileFilter: FileFilterState;
  boundingBoxes: BoundingBoxesState;
  thumbnailUrls: Record<
    string,
    {
      thumbnailUrls: {
        best: string;
      };
      expiresDateTime: string;
    }
  >;
}

const initialState: matchGroupSliceState = {
  matchSelectedTracklets: {
    results: [],
    matchGroupId: '',
    matchGroupName: '',
    eventId: '',
    apiStatus: 'idle',
    error: '',
  },
  matchGroup: {
    data: undefined,
    apiStatus: 'idle',
    error: '',
  },
  matchGroups: {
    eventId: '',
    results: [],
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    status: 'idle',
    error: '',
    sortType: '',
    sortDirection: '',
  },
  createMatchGroup: {
    status: 'idle',
    error: '',
    id: '',
  },
  updateMatchGroup: {
    status: 'idle',
    error: '',
  },
  event: {
    data: undefined,
    apiStatus: 'idle',
    error: '',
  },
  userSelectedTracklets: undefined,
  userSelectedTrackletFile: {
    file: undefined,
    selectedTracklet: undefined,
    apiStatus: 'idle',
    error: '',
  },
  fileFilter: {
    fileNames: [],
    selectedFileNames: [],
    displayString: '',
    fileIds: [],
  },
  boundingBoxes: {
    data: [],
    apiStatus: 'idle',
    error: '',
  },
  thumbnailUrls: ls.get('thumbnailUrls') || {},
};

export const matchGroupSlice = createAppSlice({
  name: 'matchGroup',
  initialState,
  reducers: (create) => {
    const createHttpThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>(); // TODO: Fix getState type
    return {
      getBoundingBoxes: createHttpThunk(
        async (
          {
            fileId,
            trackletId,
            startTimeMs,
            stopTimeMs,
            type,
          }: {
            fileId?: string;
            trackletId?: string;
            startTimeMs?: number;
            stopTimeMs?: number;
            type?: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.get<GetBoundingBoxesResponse>(signal)(
            `bounding-boxes${qs.stringify(
              { fileId, trackletId, startTimeMs, stopTimeMs, type },
              { addQueryPrefix: true }
            )}`,
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.boundingBoxes.apiStatus = 'loading';
            state.boundingBoxes.data = [];
            state.boundingBoxes.error = '';
          },
          fulfilled: (state, action) => {
            state.boundingBoxes.apiStatus = 'idle';
            state.boundingBoxes.data = action.payload.results;
            state.boundingBoxes.error = '';
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get Bounding Boxes failed',
              action.error.message
            );
            state.boundingBoxes.apiStatus = 'failure';
            state.boundingBoxes.data = [];
            state.boundingBoxes.error = action.error.message;
          },
        }
      ),
      setThumbnails: create.reducer(
        (
          state,
          action: PayloadAction<
            Record<
              string,
              {
                thumbnailUrls: {
                  best: string;
                };
                expiresDateTime: string;
              }
            >
          >
        ) => {
          state.thumbnailUrls = action.payload;
          ls.set('thumbnailUrls', state.thumbnailUrls);
        }
      ),
      setSelectedTracklet: create.reducer(
        (state, action: PayloadAction<{ tracklet: Tracklet }>) => {
          const found = state.userSelectedTracklets?.find(
            (item) => item.trackletId === action.payload.tracklet?.trackletId
          );
          if (found) {
            state.userSelectedTracklets = state.userSelectedTracklets?.filter(
              (item) => item.trackletId !== action.payload.tracklet?.trackletId
            );
          } else {
            state.userSelectedTracklets = (
              state.userSelectedTracklets ?? []
            ).concat(action.payload.tracklet);
          }
        }
      ),
      setTimelineProject: create.reducer(
        (state, action: PayloadAction<{ timeline: Tracklet[] }>) => {
          if (state.matchGroup.data?.timelineProject) {
            state.matchGroup.data.timelineProject.groups =
              action.payload.timeline.map((tracklet) => ({
                id: uuidV4(),
                name: 'Default Group',
                tracklets: [
                  {
                    ...tracklet,
                    startTimeMs: tracklet.startTimeMs ?? 0,
                    stopTimeMs: tracklet.stopTimeMs ?? 0,
                  },
                ],
              }));
          } else {
            console.error('No timeline project found in', state);
          }
        }
      ),
      selectAllTracklets: create.reducer(
        (state, action: PayloadAction<{ tracklets: Tracklet[] }>) => {
          state.userSelectedTracklets = action.payload.tracklets;
        }
      ),
      unselectAllTracklets: create.reducer((state) => {
        state.userSelectedTracklets = [];
      }),
      getEvent: createHttpThunk(
        async (
          {
            eventId,
          }: {
            eventId: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const response = await http.get<GetEventResponse>(signal)(
            `/event/${eventId}`,
            // Fixme: getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.event.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.event = { data: action.payload.event, apiStatus: 'idle' };
          },
          rejected: (state, action) => {
            if (action.error.message !== 'Aborted') {
              createSnackNotification(
                AlertLevel.Error,
                'Get event failed',
                action.error.message
              );
              state.event.apiStatus = 'failure';
            } else {
              state.event.apiStatus = 'idle';
            }
          },
        }
      ),
      getMatchGroupSelectedTracklets: createHttpThunk(
        async ({ matchGroupId }: { matchGroupId: string }, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const response =
            await http.get<GetMatchGroupSelectedTrackletsResponse>(signal)(
              `match-groups/${matchGroupId}/selected-tracklets/${qs.stringify(
                {
                  times: true,
                },
                { addQueryPrefix: true }
              )}`,
              {
                // TODO: Fix getState type
                Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
              }
            );

          return response.data;
        },
        {
          pending: (state) => {
            state.matchSelectedTracklets.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.matchSelectedTracklets = {
              ...action.payload,
              apiStatus: 'idle',
            };
            state.fileFilter.fileNames =
              action.payload?.results?.map((result) => result.fileName) ?? [];
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get File failed',
              action.error.message
            );
            state.matchGroup.apiStatus = 'failure';
          },
        }
      ),
      getMatchGroup: createHttpThunk(
        async ({ matchGroupId }: { matchGroupId: string }, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.get<GetMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.matchGroup.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.matchGroup = {
              data: action.payload.matchGroup,
              apiStatus: 'idle',
            };
          },
          rejected: (state) => {
            state.matchGroup.apiStatus = 'failure';
          },
        }
      ),
      getMatchGroups: createHttpThunk(
        async ({ eventId }: GetMatchGroupsParams, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const searchEventsQuery: SearchMatchGroupsQueryParams = {
            eventId: eventId,
            pageSize: 10000,
            currentPage: 1,
          };
          const response = await http.get<GetMatchGroupsResponse>(signal)(
            `/match-groups/${qs.stringify(searchEventsQuery, {
              addQueryPrefix: true,
            })}`,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state) => {
            state.matchGroups.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.matchGroups = { ...action.payload, status: 'idle' };
          },
          rejected: (state) => {
            state.matchGroups.status = 'failure';
          },
        }
      ),
      getFile: createHttpThunk(
        async ({ tracklet }: { tracklet: Tracklet }, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          const response = await http.get<GetFileResponse>(signal)(
            `/file/${tracklet.fileId}`,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          return response.data;
        },
        {
          pending: (state) => {
            state.userSelectedTrackletFile.apiStatus = 'loading';
          },
          fulfilled: (state, action) => {
            state.userSelectedTrackletFile = {
              file: action.payload.file,
              selectedTracklet: action.meta.arg.tracklet,
              apiStatus: 'idle',
            };
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Get File failed',
              action.error.message
            );
            state.userSelectedTrackletFile.apiStatus = 'failure';
          },
        }
      ),
      createMatchGroup: createHttpThunk(
        async (
          {
            name,
            eventId,
          }: {
            name: string;
            eventId: string;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.post<CreateMatchGroupResponse>(signal)(
            '/match-groups',
            {
              name,
              eventId,
            },
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.createMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.createMatchGroup = {
              status: 'idle',
              id: action.payload.matchGroup?.id ?? '',
            };
            createSnackNotification(
              AlertLevel.Success,
              'Create Match Group',
              `Match Group ${action.payload.matchGroup?.name} was created successfully`
            );
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Create Match Group failed',
              action.error.message
            );
            state.createMatchGroup.status = 'failure';
          },
        }
      ),
      updateMatchGroup: createHttpThunk(
        async (
          {
            matchGroupId,
            trackletId,
            referenceTrackletId,
            searchName,
            matchGroupName,
            rename,
          }: {
            matchGroupId?: string;
            trackletId?: string;
            referenceTrackletId?: string;
            searchName?: string;
            matchGroupName?: string;
            rename?: boolean;
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;

          let requestBody;
          if (rename) {
            requestBody = { name: matchGroupName };
          } else {
            requestBody = {
              searches: [
                {
                  referenceTrackletId,
                  id: trackletId,
                  searchName,
                  searchTime: new Date().toISOString(),
                },
              ],
            };
          }

          const response = await http.patch<CreateMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            requestBody,
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );
          return response.data;
        },
        {
          pending: (state) => {
            state.updateMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.updateMatchGroup = { status: 'idle' };
            createSnackNotification(
              AlertLevel.Success,
              'Update Match Group',
              `Match Group ${action.payload.matchGroup?.name} was updated successfully`
            );
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Update Match Group failed',
              action.error.message
            );
            state.updateMatchGroup.status = 'failure';
          },
        }
      ),
      deleteMatchGroup: createHttpThunk(
        async (matchGroupId: string, thunkAPI) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.delete<DeleteMatchGroupResponse>(signal)(
            `/match-groups/${matchGroupId}`,
            undefined,
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          fulfilled: (_state, action) => {
            createSnackNotification(
              AlertLevel.Success,
              'Delete match-group success',
              action.payload.message
            );
          },
          rejected: (_state, _action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Delete match-group error',
              'Delete Match-group Failed'
            );
          },
        }
      ),
      clearSelectedTracklets: create.reducer((state) => {
        state.userSelectedTracklets = [];
      }),
      deleteSelectedTracklets: createHttpThunk(
        async (
          {
            matchGroupId,
            selectedTrackletIds,
          }: {
            matchGroupId: string;
            selectedTrackletIds: string[];
          },
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            dispatch,
            extra: { http },
          } = thunkAPI;
          const response = await http.delete<DeleteTrackletsResponse>(signal)(
            `/match-groups/${matchGroupId}/selected-tracklets`,
            { selectedTrackletIds },
            // TODO: Fix getState type
            { Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}` }
          );

          const timelineSlice = (getState() as RootState).timeline;
          const newTimeline = timelineSlice.timeline.filter(
            (t) => !selectedTrackletIds.includes(t.trackletId)
          );
          dispatch({
            type: 'timeline/setTimeline',
            payload: { timeline: newTimeline },
          });

          return response.data;
        },
        {
          pending: (state) => {
            state.updateMatchGroup.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.updateMatchGroup = { status: 'idle' };
            if (state.matchGroup.data?.timelineProject?.groups) {
              state.matchGroup.data.timelineProject.groups =
                state.matchGroup.data?.timelineProject?.groups?.filter(
                  (g) =>
                    !g.tracklets.some((t) =>
                      action.meta.arg.selectedTrackletIds.includes(t.trackletId)
                    )
                );
            }

            createSnackNotification(
              AlertLevel.Success,
              'Delete Selected Detections',
              action.payload.message
            );
            state.matchSelectedTracklets.results =
              state.matchSelectedTracklets?.results?.filter(
                (tracklet) =>
                  !state.userSelectedTracklets?.some(
                    (selectedTracklet) =>
                      selectedTracklet.trackletId === tracklet.trackletId
                  )
              );
            state.userSelectedTracklets = [];
          },
          rejected: (state, action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Delete Selected Detections failed',
              action.error.message
            );
            state.updateMatchGroup.status = 'failure';
          },
        }
      ),
      setFileNameFilter: create.reducer(
        (
          state,
          action: PayloadAction<{
            selectedFileNames: string[];
            displayString: string;
            selectedFileIds: string[];
          }>
        ) => {
          const { selectedFileNames, displayString, selectedFileIds } =
            action.payload;
          state.fileFilter.selectedFileNames = selectedFileNames;
          state.fileFilter.displayString = displayString;
          state.fileFilter.fileIds = selectedFileIds;
          state.userSelectedTracklets = undefined;
          state.userSelectedTrackletFile = {
            file: undefined,
            apiStatus: 'idle',
            error: '',
          };
        }
      ),
      getThumbnails: createHttpThunk(
        async (
          tracklets: Array<{
            trackletId: string;
            orgId: string;
            fileId: string;
          }>,
          thunkAPI
        ) => {
          const {
            getState,
            signal,
            extra: { http },
          } = thunkAPI;
          const response = await http.post<GetThumbnailsResponse>(signal)(
            '/thumbnails',
            { tracklets },
            {
              // TODO: Fix getState type
              Authorization: `Bearer ${getApiAuthToken(getState() as RootState)}`,
            }
          );
          return response.data;
        },
        {
          fulfilled: (state, action) => {
            Object.assign(state.thumbnailUrls, action.payload.thumbnails);
            state.thumbnailUrls = Object.entries(state.thumbnailUrls).reduce(
              (acc, [key, value]) =>
                new Date(value.expiresDateTime) <= new Date()
                  ? acc
                  : { ...acc, [key]: value },
              {}
            );
            ls.set('thumbnailUrls', state.thumbnailUrls);
          },
          rejected: (_state, _action) => {
            createSnackNotification(
              AlertLevel.Error,
              'Error',
              'Get Thumbnails Failed'
            );
          },
        }
      ),
    };
  },
  extraReducers: (builder) => {
    builder
      .addCase(getEventById.pending, (state) => {
        state.event.apiStatus = 'loading';
      })
      .addCase(getEventById.fulfilled, (state, action) => {
        state.event = { data: action.payload.event, apiStatus: 'idle' };
      })
      .addCase(getEventById.rejected, (state, action) => {
        if (action.error.message !== 'Aborted') {
          state.event.apiStatus = 'failure';
        } else {
          state.event.apiStatus = 'idle';
        }
      })
      .addCase(updateMatchGroupAttributeSearch.pending, (state) => {
        state.updateMatchGroup.status = 'loading';
        state.matchGroup.apiStatus = 'loading';
      })
      .addCase(updateMatchGroupAttributeSearch.fulfilled, (state, action) => {
        state.updateMatchGroup.status = 'idle';
        state.matchGroup.apiStatus = 'idle';

        state.matchGroup.data = action.payload.matchGroup;
      })
      .addCase(updateMatchGroupAttributeSearch.rejected, (state) => {
        state.updateMatchGroup.status = 'failure';
        state.matchGroup.apiStatus = 'failure';
      });
  },
  selectors: {
    selectBoundingBoxes: (state) => state.boundingBoxes.data,
    selectUserSelectedTrackletFile: (state) => state.userSelectedTrackletFile,
    selectMatchSelectedTracklets: (state) => state.matchSelectedTracklets,
    selectUserSelectedTracklets: (state) => state.userSelectedTracklets,
    selectMatchGroup: (state) => state.matchGroup,
    selectMatchGroups: (state) => state.matchGroups,
    selectNewMatchGroupId: (state) => state.createMatchGroup.id,
    selectEvent: (state) => state.event,
    selectFileFilter: (state) => state.fileFilter,
    selectThumbnails: (state) => state.thumbnailUrls,
  },
});

export const {
  getBoundingBoxes,
  setSelectedTracklet,
  selectAllTracklets,
  unselectAllTracklets,
  getMatchGroupSelectedTracklets,
  getFile,
  getMatchGroup,
  getMatchGroups,
  createMatchGroup,
  updateMatchGroup,
  deleteMatchGroup,
  getEvent,
  deleteSelectedTracklets,
  clearSelectedTracklets,
  setFileNameFilter,
  setTimelineProject,
  getThumbnails,
  setThumbnails,
} = matchGroupSlice.actions;

export const {
  selectBoundingBoxes,
  selectUserSelectedTrackletFile,
  selectUserSelectedTracklets,
  selectMatchSelectedTracklets,
  selectMatchGroup,
  selectMatchGroups,
  selectNewMatchGroupId,
  selectEvent,
  selectFileFilter,
  selectThumbnails,
} = matchGroupSlice.selectors;
