import { Before, Given, Then, When, DataTable } from '@badeball/cypress-cucumber-preprocessor';
import { mainPage } from '../../../pages/landingPage';

Before(() => {
  cy.LoginLandingPage();
});

Given('The main page is loaded', () => {
  cy.waitMainPageIsLoaded();
});

Then('The user should see the correct column headers for Event Name and Event Time', () => {
  mainPage.verifyColumnHeaders();
});

Then('The user should see the application title Track', () => {
  mainPage.verifyAppTitle();
});

When('The user clicks on the {string} tab', (tabName: string) => {
  mainPage.clickTab(tabName);
});

Then('The {string} tab should be active', (activeTabName: string) => {
  mainPage.verifyTabIsActive(activeTabName);
});

When('The user clicks the {string} breadcrumb', (breadcrumbText: string) => {
  mainPage.clickBreadcrumb(breadcrumbText);
});

Then('The page should not navigate away', () => {
  mainPage.verifyPageNotNavigated();
});

When('The user selects the event named {string}', (eventName: string) => {
  mainPage.selectEvent(eventName);
});

Then('The following event details should be visible:', (dataTable: DataTable) => {
  const rows = dataTable.hashes() as unknown as Array<{Field: string; 'Expected Value': string}>;
  mainPage.verifyEventDetails(rows);
});
