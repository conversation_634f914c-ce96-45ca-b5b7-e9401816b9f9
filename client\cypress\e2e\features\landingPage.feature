Feature: Landing page

Background:
  Given The main page is loaded

@e2e @landing-page
Scenario: Check UI of event list
  And The user should see the correct column headers for Event Name and Event Time
  Then The user should see the application title Track

@e2e @landing-page
Scenario: Verify user can moving between Events and Files tab
  When The user clicks on the "Files" tab
  Then The "Files" tab should be active
  When The user clicks on the "Events" tab
  Then The "Events" tab should be active

@e2e @landing-page
Scenario: Verify when user clicks on the breadcrumb
  When The user clicks the "All Events" breadcrumb
  Then The page should not navigate away

@e2e @landing-page
Scenario: Verify user can see an event detail
  When The user selects the event named "May 29"
  Then The following event details should be visible:
      | Field               | Expected Value                |
      | Event Name          | May 29                        |
      | Date                | Date: 5/29/2025, 11:34 AM     |
      | Event Creator       | Event Creator: <PERSON><PERSON>    |
      | File Count          | 1 File                        |
      | Match Group Count   | 0 Match Group                 |
      | View Event Button   | View Event                    |
      | Delete Event Button | Delete Event                  |
